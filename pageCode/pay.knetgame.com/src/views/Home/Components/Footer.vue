<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { ref, watch } from "vue";
import { type IGoodsItem } from "@/api/AppApi";

interface Props {
  selectTabs: number;
  selectedGoodsItem: IGoodsItem | null;
  isDisabled: boolean;
}

const { t } = useI18n();
const isVisible = ref(true);

const props = withDefaults(defineProps<Props>(), {
  selectTabs: 1,
  selectedGoodsItem: null,
  isDisabled: false,
});

const emit = defineEmits(["submit"]);

// 监听商品选择，自动显示footer
watch(
  () => props.selectedGoodsItem,
  (newVal) => {
    if (newVal) {
      showFooter();
    }
  }
);

const showFooter = () => {
  isVisible.value = true;
};

const hideFooter = () => {
  isVisible.value = false;
};

const submit = () => {
  hideFooter();
  emit("submit");
};
</script>

<template>
  <transition name="footer-slide">
    <div class="footer" v-if="props.selectTabs == 1 && props.selectedGoodsItem">
      <div class="footer-pay-desc">
        <span class="font-bold">{{ props.selectedGoodsItem.item_name }}：</span>
        {{ props.selectedGoodsItem.item_desc }} {{ props.selectedGoodsItem.currency }}
      </div>
      <div class="pay-btn-box">
        <div class="price-box">
          <div class="sum-price-text">{{ t("message.totalPrice") }}：</div>
          <div class="price">{{ props.selectedGoodsItem.currency }} {{ props.selectedGoodsItem.item_money }}</div>
        </div>
        <div class="pay-btn" :class="{ disabled: props.isDisabled }" @click="!props.isDisabled && submit()" :disabled="props.isDisabled">{{ t("message.buyNow") }}</div>
        <div class="paypal-btn" id="googlepay-button-container"></div>
        <div class="paypal-btn" id="applepay-button-container"></div>
        <div class="paypal-btn" id="paypal-button-container"></div>
      </div>
    </div>
  </transition>
</template>

<style scoped lang="less">
.footer {
  width: 100%;
  height: 1.16rem;
  background-color: #ffbb64;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.2rem;

  @media (min-width: 751px) {
    height: 116px;
    min-width: 1200px;
    padding: 0 200px;
  }
}

// Vue过渡动画
.footer-slide-enter-active,
.footer-slide-leave-active {
  transition: transform 0.15s ease-in-out;
}

.footer-slide-enter-from {
  transform: translateY(100%);
}

.footer-slide-leave-to {
  transform: translateY(100%);
}

.footer-pay-desc {
  font-size: 0.18rem;
  color: #361f00;

  @media (min-width: 751px) {
    font-size: 18px;
  }

  .font-bold {
    font-weight: bold;
  }
}

.pay-btn-box {
  flex-shrink: 0;
  min-width: 4.37rem;
  height: 0.79rem;
  border-radius: 0.6rem;
  background-color: #ffffff;
  border: 2px solid #fba139;
  font-size: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (min-width: 751px) {
    min-width: 348px;
    height: 65px;
    font-size: 20px;
    border-radius: 30px;
  }

  .price-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0.1rem;

    @media (min-width: 751px) {
      padding: 0 20px;
    }

    .sum-price-text {
      color: #ff9d33;
      text-align: center;

      @media (min-width: 751px) {
        font-size: 20px;
        color: #ff9d33;
      }
    }

    .price {
      color: #ff9d33;
      text-align: center;
      font-weight: bold;

      @media (min-width: 751px) {
        font-size: 20px;
        color: #ff9d33;
        font-weight: bold;
        font-family: "Mi Sans";
      }
    }
  }

  .pay-btn {
    width: 1.83rem;
    height: 0.79rem;
    line-height: 0.79rem;
    border-radius: 0.6rem;
    filter: drop-shadow(0 0 1px #fe9c34);
    background-image: linear-gradient(90deg, #fe9c34 0%, #fe9c34 100%);
    color: #ffffff;
    font-weight: bold;
    font-family: "Mi Sans";
    text-align: center;
    cursor: pointer;

    @media (min-width: 751px) {
      width: 146px;
      height: 65px;
      line-height: 65px;
      font-size: 20px;
      border-radius: 30px;
    }

    &.disabled {
      filter: grayscale(1);
      cursor: not-allowed;
    }
  }

  .paypal-btn {
    height: 65px;
  }
}
</style>
