<template>
  <div class="login-container">
    <!-- 登录弹窗 -->
    <Popup ref="popupLoginRef">
      <div class="popup-login">
        <div class="close" @click="closeLoginPopup"></div>

        <div class="login-form">
          <div class="field email-box">
            <div class="label">{{ t("message.account") }}：</div>
            <div class="input-box">
              <input type="text" :placeholder="t('message.pleaseEnterAccount')" v-model="loginForm.email" @keyup.enter="handleLogin" />
            </div>
          </div>

          <div class="field password-box">
            <div class="label">{{ t("message.password") }}：</div>
            <div class="input-box">
              <input type="password" :placeholder="t('message.pleaseEnterPassword')" v-model="loginForm.pass" @keyup.enter="handleLogin" />
            </div>
          </div>
        </div>

        <div class="login-btn" @click="handleLogin">{{ t("message.login") }}</div>

        <div class="other-type-box">
          <div class="line"></div>
          <div class="other-type">{{ t("message.otherLoginMethods") }}</div>
          <div class="line"></div>
          <div class="gg-login" @click="handleGoogleLoginClick" title="Google 登录"></div>
          <div class="fb-login" @click="handleFacebookLoginClick" title="Facebook 登录"></div>
        </div>

        <!-- 加载状态提示 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-text">{{ t("message.loggingIn") }}</div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Popup from "@/components/Popup.vue";
import AppApi, { type ILoginParams } from "@/api/AppApi";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";
import { message } from "ant-design-vue";
import { useThirdPartyLogin } from "@/hooks/useThirdPartyLogin";
import md5 from "md5";
import { useAppStore } from "@/stores/app";
import { useI18n } from "vue-i18n";

const emit = defineEmits(["loginSuccess"]);
const { t } = useI18n();
const appStore = useAppStore();

// 登录弹窗显示状态
const showLoginPopup = ref(false);

// 登录表单数据
const defaultLoginForm = {
  email: "",
  pass: "",
};
const loginForm = ref<ILoginParams>(defaultLoginForm);

// 弹窗引用
const popupLoginRef = ref<InstanceType<typeof Popup> | null>(null);

// 第三方登录 hook
const { isLoading, handleGoogleLogin, handleFacebookLogin } = useThirdPartyLogin();

// 打开登录弹窗
const openLoginPopup = () => {
  popupLoginRef.value?.open();
  showLoginPopup.value = true;
};

// 关闭登录弹窗
const closeLoginPopup = () => {
  popupLoginRef.value?.close();
  showLoginPopup.value = false;
  // 清空表单
  loginForm.value = defaultLoginForm;
};

// 处理登录
async function handleLogin() {
  if (!loginForm.value.email || !loginForm.value.pass) {
    message.error("请输入邮箱和密码");
    return;
  }

  try {
    // 创建登录数据，密码进行 MD5 加密
    const loginData = {
      email: loginForm.value.email,
      pass: md5(loginForm.value.pass), // 对密码进行 MD5 加密
    };

    const res = await AppApi.login(loginData);

    if (res.code === ApiResultCode.OK) {
      appStore.setUserInfo(res.data);
      message.success("登录成功");
      emit("loginSuccess", res.data);
      closeLoginPopup();
    } else {
      message.error(res.message);
    }
  } catch (error) {
    console.error("登录失败:", error);
    message.error("登录失败，请稍后重试");
  }
}

// 处理Google登录
const handleGoogleLoginClick = async () => {
  try {
    await handleGoogleLogin();
    // 登录成功后关闭弹窗
    closeLoginPopup();
  } catch (error) {
    console.error("Google login failed:", error);
  }
};

// 处理Facebook登录
const handleFacebookLoginClick = async () => {
  try {
    await handleFacebookLogin();
    // 登录成功后关闭弹窗
    closeLoginPopup();
  } catch (error) {
    console.error("Facebook login failed:", error);
  }
};

// 暴露方法给父组件使用
defineExpose({
  openLoginPopup,
  closeLoginPopup,
});
</script>

<style scoped lang="less">
// PC端样式
@media (min-width: 751px) {
  .popup-login {
    position: relative;
    padding: 0 82px;
    width: 539px;
    height: 374px;
    border-radius: 10px;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    flex-direction: column;

    .close {
      position: absolute;
      top: 13px;
      right: 10px;
      width: 14px;
      height: 11px;
      background: url("@/assets/imgs/pc/pop/close.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }

    .login-form {
      width: 377px;
      margin-top: 55px;

      .field {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }

      .label {
        height: 35px;
        line-height: 35px;
        font-size: 20px;
        color: #000000;
        text-align: left;
      }

      .input-box {
        flex: 1;
        height: 35px;
        border-radius: 3px;
        background-color: #ffffff;
        font-size: 16px;
        color: #000000;
        border: 1px solid #ddd;

        input {
          width: 100%;
          height: 100%;
          text-indent: 1em;
          border: none;
          outline: none;

          &:focus {
            border-color: #1890ff;
          }
        }
      }

      .email-box,
      .password-box {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
    }

    .login-btn {
      width: 146px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
      filter: drop-shadow(0 0 1px rgba(255, 150, 0, 0.7));
      background-image: linear-gradient(90deg, rgba(255, 157, 51, 0.9999999999999999) 0%, rgba(255, 157, 51, 0.996078431372549) 100%);
      font-size: 20px;
      color: #ffffff;
      font-weight: bold;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-image: linear-gradient(90deg, rgba(255, 157, 51, 0.9) 0%, rgba(255, 157, 51, 0.9) 100%);
      }
    }

    .other-type-box {
      width: 100%;
      position: absolute;
      top: 243px;
      left: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 82px;

      .line {
        width: 116px;
        height: 1px;
        border-radius: 10px;
        background-color: #e5e5e5;
      }

      .other-type {
        opacity: 0.502;
        font-size: 14px;
        color: #000000;
        font-family: "Mi Sans";
        text-align: center;
      }

      .gg-login {
        position: absolute;
        top: 35px;
        left: 190px;
        width: 76px;
        height: 76px;
        background: url("@/assets/imgs/pc/pop/gg.png") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      .fb-login {
        position: absolute;
        top: 35px;
        left: 275px;
        width: 76px;
        height: 76px;
        background: url("@/assets/imgs/pc/pop/fb.png") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    // 加载状态样式
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      z-index: 10;

      .loading-text {
        font-size: 18px;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

// 移动端样式
@media (max-width: 750px) {
  .popup-login {
    position: relative;
    padding: 0 0.82rem;
    width: 5.39rem;
    height: 3.74rem;
    border-radius: 0.1rem;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    flex-direction: column;

    .close {
      position: absolute;
      top: 0.13rem;
      right: 0.1rem;
      width: 0.14rem;
      height: 0.11rem;
      background: url("@/assets/imgs/pc/pop/close.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }

    .login-form {
      width: 3.77rem;
      margin-top: 0.55rem;

      .label {
        width: 0.65rem;
        height: 0.35rem;
        line-height: 0.35rem;
        font-size: 0.2rem;
        color: #000000;
        text-align: left;
      }

      .input-box {
        width: 3.19rem;
        height: 0.35rem;
        border-radius: 0.03rem;
        background-color: #ffffff;
        font-size: 0.16rem;
        color: #000000;
        border: 1px solid #ddd;

        input {
          width: 100%;
          height: 100%;
          text-indent: 1em;
          border: none;
          outline: none;

          &:focus {
            border-color: #1890ff;
          }
        }
      }

      .email-box,
      .password-box {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.2rem;
      }
    }

    .login-btn {
      position: absolute;
      top: 1.68rem;
      left: 2.05rem;
      width: 1.46rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      filter: drop-shadow(0 0 1px rgba(255, 150, 0, 0.7));
      background-image: linear-gradient(90deg, rgba(255, 157, 51, 0.9999999999999999) 0%, rgba(255, 157, 51, 0.996078431372549) 100%);
      font-size: 0.2rem;
      color: #ffffff;
      font-weight: bold;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-image: linear-gradient(90deg, rgba(255, 157, 51, 0.9) 0%, rgba(255, 157, 51, 0.9) 100%);
      }
    }

    .other-type-box {
      width: 100%;
      position: absolute;
      top: 2.43rem;
      left: 0.05rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0.82rem;

      .line {
        width: 1.16rem;
        height: 1px;
        border-radius: 0.1rem;
        background-color: #e5e5e5;
      }

      .other-type {
        opacity: 0.502;
        font-size: 0.14rem;
        color: #000000;
        font-family: "Mi Sans";
        text-align: center;
      }

      .gg-login {
        position: absolute;
        top: 0.35rem;
        left: 1.9rem;
        width: 0.76rem;
        height: 0.76rem;
        background: url("@/assets/imgs/pc/pop/gg.png") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      .fb-login {
        position: absolute;
        top: 0.35rem;
        left: 2.75rem;
        width: 0.76rem;
        height: 0.76rem;
        background: url("@/assets/imgs/pc/pop/fb.png") no-repeat;
        background-size: 100% 100%;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    // 移动端加载状态样式
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.1rem;
      z-index: 10;

      .loading-text {
        font-size: 0.18rem;
        color: #666;
        font-weight: 500;
      }
    }
  }
}
</style>
