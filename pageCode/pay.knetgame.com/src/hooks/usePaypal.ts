import { nextTick, ref } from "vue";
import { loadScript as paypalLoadScript } from "@paypal/paypal-js";
import { useI18n } from "vue-i18n";
import AppUtils from "@/utils/AppUtils";
import { message } from "ant-design-vue";
import AppApi from "@/api/AppApi";

const captureUrl =
  "https://seaginger.gamehaza.com/payget/paypalkingnethknotifynew";
const secretKey = "18203edc4e7dd25bdee3363ae55c9ce6";
const sdkLoaded = ref(false);
let paymentsClient: any = null;
let allowedPaymentMethods: any = null;
let merchantInfo: any = null;
let currentTransactionInfo = {
  displayItems: [{ label: "Subtotal", type: "SUBTOTAL", price: "0.99" }],
  totalPriceStatus: "FINAL",
  totalPrice: "0.99",
  totalPriceLabel: "Total",
  currencyCode: "USD",
  countryCode: "US",
};

export function usePaypal() {
  let _payClick: any;

  const { t, locale } = useI18n();

  /**
   * 初始化
   */
  const initPaypal = async (initParams: any, payList: any, payClick: any) => {
    _payClick = payClick;
    await paypalLoadScript({ ...initParams });
    await nextTick();

    if (payList?.length) {
      payList.forEach((item: any) => {
        switch (item.pay) {
          case "googlepay":
            initGooglePay(item);
            break;
          case "applepay":
            setupApplepay(item.btnName);
            break;
          default:
            constructButton(window.paypal.FUNDING[item.pay], item.btnName);
            break;
        }
      });
    }
  };

  /**
   * Apple Pay
   */
  async function setupApplepay(btnName: string) {
    await loadApplePayScripts();
    const applepay = window.paypal.Applepay();
    const {
      isEligible,
      countryCode,
      currencyCode,
      merchantCapabilities,
      supportedNetworks,
    } = await applepay.config();

    if (!isEligible) {
      throw new Error("applepay is not eligible");
    }

    if (!window.ApplePaySession.canMakePayments()) {
      console.error("This device is not capable of making Apple Pay payments");
    }

    document.getElementById(btnName)!.innerHTML =
      '<apple-pay-button id="btn-appl" buttonstyle="black" type="buy" locale="en">';
    document.getElementById("btn-appl")?.addEventListener("click", onClick);

    async function onClick() {
      try {
        const paymentRequest = {
          countryCode: "US",
          currencyCode: currentTransactionInfo.currencyCode,
          merchantCapabilities,
          supportedNetworks,
          requiredBillingContactFields: [
            "name",
            "phone",
            "email",
            "postalAddress",
          ],
          requiredShippingContactFields: [],
          total: {
            label: "Demo (Card is not charged)",
            amount: currentTransactionInfo.totalPrice,
            type: "final",
          },
        };

        console.log("paymentRequest555", paymentRequest);

        // eslint-disable-next-line no-undef
        let session = new window.ApplePaySession(4, paymentRequest);

        session.onvalidatemerchant = (event: any) => {
          console.log("validationURL", event.validationURL);
          applepay
            .validateMerchant({
              validationUrl: event.validationURL,
            })
            .then((payload: any) => {
              console.log("payload.merchantSession", payload.merchantSession);
              session.completeMerchantValidation(payload.merchantSession);
            })
            .catch((err: Error) => {
              console.error(err);
              session.abort();
            });
        };

        session.onpaymentmethodselected = () => {
          session.completePaymentMethodSelection({
            newTotal: paymentRequest.total,
          });
        };

        session.onpaymentauthorized = async (event: any) => {
          try {
            const { paypal_order_id, notify_url, order_id } = await _payClick(
              {},
              "applepay"
            );
            if (!paypal_order_id) {
              throw new Error("error creating order");
            }
            /**
             * Confirm Payment
             */
            await applepay.confirmOrder({
              orderId: paypal_order_id,
              token: event.payment.token,
              billingContact: event.payment.billingContact,
              shippingContact: event.payment.shippingContact,
            });

            /*
             * Capture order (must currently be made on server)
             */
            // await fetch(`/api/orders/${id}/capture`, {
            //   method: "POST",
            // });
            session.completePayment({
              status: window.ApplePaySession.STATUS_SUCCESS,
            });
          } catch (err) {
            console.error(err);
            session.completePayment({
              status: window.ApplePaySession.STATUS_FAILURE,
            });
          }
        };

        session.oncancel = () => {
          console.log("Apple Pay Cancelled !!");
        };

        session.begin();
      } catch (error) {
        console.log("触发trycatch");
        console.log("error", error);
      }
    }
  }

  // 辅助函数：根据支付方式设置按钮颜色
  const getButtonColor = (fundingSource: any) => {
    const paypal = window.paypal;
    switch (fundingSource) {
      case paypal.FUNDING.PAYPAL:
        return "blue";
      case paypal.FUNDING.APPLEPAY:
        return "black"; // Apple Pay 官方推荐黑色
      case paypal.FUNDING.GOOGLEPAY:
        return "white"; // Google Pay 支持黑白两种样式
      default:
        return "";
    }
  };

  // paypal支付按钮
  const constructButton = (fundingSource: any, btnName: string) => {
    const paypal = window.paypal;
    if (paypal && paypal.Buttons) {
      const button = paypal.Buttons({
        style: {
          color: getButtonColor(fundingSource), // 根据支付方式设置颜色
          layout: "horizontal",
          height: 40, // 设置按钮的高度
        },
        fundingSource: fundingSource,
        createOrder: (e: any) => {
          return _payClick(e, "paypal");
        },
        onApprove: async (data: any, actions: any) => {
          // 用户同意支付后的逻辑
          return actions.order.capture().then(function (details: any) {
            // 支付成功的处理逻辑
            message.success(t("message.paySuccess"));
          });
        },

        onCancel: (data: any) => {
          // 用户取消支付的逻辑
          message.info(t("message.payCancel"));
        },
        onError: (err: Error) => {
          // 错误处理逻辑
          console.error(err);
        },
      });
      // Check if the button is eligible
      if (button.isEligible()) {
        // Render the standalone button for that funding source
        button.render(`#${btnName}`).catch((error: any) => {
          console.error("Failed to render the PayPal Buttons", error);
        });
      }
    }
  };

  async function loadGooglePayScripts() {
    if (sdkLoaded.value) return;
    await loadScript("https://pay.google.com/gp/p/js/pay.js");

    sdkLoaded.value = true;
  }

  async function loadApplePayScripts() {
    if (sdkLoaded.value) return;
    await loadScript(
      "https://applepay.cdn-apple.com/jsapi/v1/apple-pay-sdk.js"
    );

    sdkLoaded.value = true;
  }

  function loadScript(src: string) {
    return new Promise<void>((resolve, reject) => {
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement("script");
      script.src = src;
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load ${src}`));
      document.head.appendChild(script);
    });
  }

  function getTransactionInfo(info: any) {
    return {
      displayItems: [
        { label: "Subtotal", type: "SUBTOTAL", price: info.totalPrice },
      ],
      totalPriceStatus: info.totalPriceStatus || "FINAL",
      totalPrice: info.totalPrice,
      totalPriceLabel: "Total",
      currencyCode: info.currencyCode,
      countryCode: info.countryCode || "US",
    };
  }

  async function initGooglePay(item: any) {
    await loadGooglePayScripts();
    if (!paymentsClient) {
      paymentsClient = new window.google.payments.api.PaymentsClient({
        environment: "TEST",
        paymentDataCallbacks: {
          onPaymentAuthorized: onGooglePaymentAuthorized,
        },
      });
    }

    const config = await window.paypal.Googlepay().config();
    allowedPaymentMethods = config.allowedPaymentMethods;
    merchantInfo = config.merchantInfo;

    const isReady = await paymentsClient.isReadyToPay({
      apiVersion: 2,
      apiVersionMinor: 0,
      allowedPaymentMethods,
    });

    if (isReady.result) {
      const button = paymentsClient.createButton({
        ...item.btnOptions,
        onClick: onGooglePaymentButtonClicked,
      });
      const container = document.getElementById(item.btnName);
      if (container && container.childElementCount === 0) {
        container.appendChild(button);
      }
    }
  }

  function updateTransactionInfo(info: any) {
    currentTransactionInfo = getTransactionInfo(info);
  }

  async function onGooglePaymentButtonClicked() {
    if (!currentTransactionInfo) {
      alert("Product not configured");
      return;
    }
    const paymentDataRequest = {
      apiVersion: 2,
      apiVersionMinor: 0,
      allowedPaymentMethods,
      transactionInfo: currentTransactionInfo,
      merchantInfo,
      callbackIntents: ["PAYMENT_AUTHORIZATION"],
    };

    await paymentsClient.loadPaymentData(paymentDataRequest);
  }

  async function onGooglePaymentAuthorized(paymentData: any) {
    try {
      console.log(paymentData)
      const payResult = await _payClick({}, "googlepay");
      if (!payResult) {
        return;
      }
      const { paypal_order_id, order_id } = payResult;
      if (!paypal_order_id) {
        return { transactionState: "ERROR" };
      }
      const { status } = await window.paypal.Googlepay().confirmOrder({
        orderId: paypal_order_id,
        paymentMethodData: paymentData.paymentMethodData,
      });
      if (status === "APPROVED") {
        const params = {
          order_id,
          paypal_order_id,
        };
        AppApi.capture(captureUrl, {
          ...params,
          sign: AppUtils.generateSign(params, secretKey),
        });
        return { transactionState: "SUCCESS" };
      } else {
        return { transactionState: "ERROR" };
      }
    } catch (err: any) {
      return {
        transactionState: "ERROR",
        error: { message: err.message },
      };
    }
  }

  return {
    initPaypal,
    updateTransactionInfo,
  };
}
